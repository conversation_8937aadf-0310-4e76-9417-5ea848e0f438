<?xml version="1.1" encoding="UTF-8"?>
<hudson.plugins.sectioned__view.SectionedView plugin="sectioned-view@1.30">
  <name>dev-na-battlefieldgame Autotests</name>
  <filterExecutors>false</filterExecutors>
  <filterQueue>false</filterQueue>
  <properties class="hudson.model.View$PropertyList"/>
  <sections>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>Orchestration</name>
      <includeRegex>(dev-na-battlefieldgame)\.autotest.*\.start</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
        <hudson.views.BuildButtonColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>Utility jobs</name>
      <includeRegex>dev-na-battlefieldgame.(build.selector|bilbo.register-.*-autotestutils|autotest-to-integration.code)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>Manual Tests</name>
      <includeRegex>(dev-na-battlefieldgame)\.autotest.*.*\.manual.*</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>boottests</name>
      <includeRegex>((dev-na-battlefieldgame)\.autotest.*.*\.boottests.*(job).*)|(dev-na-battlefieldgame.boottests.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>smoketests</name>
      <includeRegex>((dev-na-battlefieldgame)\.autotest.*.*\.smoketests.*(job).*)|(dev-na-battlefieldgame.smoketests.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
  </sections>
</hudson.plugins.sectioned__view.SectionedView>