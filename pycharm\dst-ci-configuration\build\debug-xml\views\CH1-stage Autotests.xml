<?xml version="1.1" encoding="UTF-8"?>
<hudson.plugins.sectioned__view.SectionedView plugin="sectioned-view@1.30">
  <name>CH1-stage Autotests</name>
  <filterExecutors>false</filterExecutors>
  <filterQueue>false</filterQueue>
  <properties class="hudson.model.View$PropertyList"/>
  <sections>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>Orchestration</name>
      <includeRegex>(CH1-stage)\.autotest.*\.start</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
        <hudson.views.BuildButtonColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>Utility jobs</name>
      <includeRegex>CH1-stage.(build.selector|bilbo.register-.*-autotestutils|autotest-to-integration.code)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>lkg_auto</name>
      <includeRegex>((CH1-stage)\.autotest.*.*\.lkg_auto.*(job).*)|(CH1-stage.lkg_auto.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>lkg_qv</name>
      <includeRegex>((CH1-stage)\.autotest.*.*\.lkg_qv.*(job).*)|(CH1-stage.lkg_qv.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>lkg_qv_la</name>
      <includeRegex>((CH1-stage)\.autotest.*.*\.lkg_qv_la.*(job).*)|(CH1-stage.lkg_qv_la.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>lkg_checkmate</name>
      <includeRegex>((CH1-stage)\.autotest.*.*\.lkg_checkmate.*(job).*)|(CH1-stage.lkg_checkmate.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_highpriority_performance_setup_AlphaValidation_BFLabs</name>
      <includeRegex>((CH1-stage)\.autotest.*.*\.pt_perf_highpriority_performance_setup_AlphaValidation_BFLabs.*(job).*)|(CH1-stage.pt_perf_highpriority_performance_setup_AlphaValidation_BFLabs.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_performance_setup_trinity_stage_AlphaValidation_Trinity_BFLabs</name>
      <includeRegex>((CH1-stage)\.autotest.*.*\.pt_perf_performance_setup_trinity_stage_AlphaValidation_Trinity_BFLabs.*(job).*)|(CH1-stage.pt_perf_performance_setup_trinity_stage_AlphaValidation_Trinity_BFLabs.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_flythroughs_XBSS_performance_setup_ch1_stage_AlphaValidation_XBSS_BFLabs</name>
      <includeRegex>((CH1-stage)\.autotest.*.*\.pt_perf_flythroughs_XBSS_performance_setup_ch1_stage_AlphaValidation_XBSS_BFLabs.*(job).*)|(CH1-stage.pt_perf_flythroughs_XBSS_performance_setup_ch1_stage_AlphaValidation_XBSS_BFLabs.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>large_scale_autoplaytest_setup_ch1_stage</name>
      <includeRegex>((CH1-stage)\.autotest.*.*\.large_scale_autoplaytest_setup_ch1_stage.*(job).*)|(CH1-stage.large_scale_autoplaytest_setup_ch1_stage.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_AlphaValidation_UltSpec_BFlabs_performance_setup</name>
      <includeRegex>((CH1-stage)\.autotest.*.*\.pt_perf_AlphaValidation_UltSpec_BFlabs_performance_setup.*(job).*)|(CH1-stage.pt_perf_AlphaValidation_UltSpec_BFlabs_performance_setup.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_highpriority_minspec_performance_setup_AlphaValidation_MinSpec_BFLabs</name>
      <includeRegex>((CH1-stage)\.autotest.*.*\.pt_perf_highpriority_minspec_performance_setup_AlphaValidation_MinSpec_BFLabs.*(job).*)|(CH1-stage.pt_perf_highpriority_minspec_performance_setup_AlphaValidation_MinSpec_BFLabs.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
    <hudson.plugins.sectioned__view.ListViewSection>
      <jobFilters/>
      <name>pt_perf_recspec_highpriority_performance_setup_AlphaValidation_RecSpec_BFLabs</name>
      <includeRegex>((CH1-stage)\.autotest.*.*\.pt_perf_recspec_highpriority_performance_setup_AlphaValidation_RecSpec_BFLabs.*(job).*)|(CH1-stage.pt_perf_recspec_highpriority_performance_setup_AlphaValidation_RecSpec_BFLabs.p4counterupdater)</includeRegex>
      <executingRegexOnAllJobs>false</executingRegexOnAllJobs>
      <width>FULL</width>
      <alignment>CENTER</alignment>
      <columns>
        <hudson.views.StatusColumn/>
        <hudson.views.WeatherColumn/>
        <hudson.views.JobColumn/>
        <hudson.views.LastSuccessColumn/>
        <hudson.views.LastFailureColumn/>
        <hudson.views.LastDurationColumn/>
      </columns>
    </hudson.plugins.sectioned__view.ListViewSection>
  </sections>
</hudson.plugins.sectioned__view.SectionedView>