



With this version:
git checkout -f 440a78fa5884d4c91a5bd1dc1090ae2e3b07d700 # timeout=10
Commit message: "fix: [COBRA-0000] do not rebuild if builds is null"
We got:
12:00:36  NeedsRebuildData: Jenkins item for job "CH1-content-dev-first-patch.patchdata-combine.bfdata.win64" is null.
[Pipeline] echo
12:00:36  NeedsRebuildData: Jenkins item for job "CH1-content-dev-first-patch.patchdata-combine.bfdata.ps5" is null.
[Pipeline] echo
12:00:36  NeedsRebuildData: Jenkins item for job "CH1-content-dev-first-patch.patchdata-combine.bfdata.xbsx" is null.
[Pipeline] parallel
[Pipeline] { (Branch: CH1-content-dev-first-patch.patchdata-combine.bfdata.win64)
[Pipeline] { (Branch: CH1-content-dev-first-patch.patchdata-combine.bfdata.ps5)
[Pipeline] { (Branch: CH1-content-dev-first-patch.patchdata-combine.bfdata.xbsx)
[Pipeline] build
[Pipeline] }
12:00:36 
 Failed in branch CH1-content-dev-first-patch.patchdata-combine.bfdata.win64
[Pipeline] build
[Pipeline] }
12:00:36  Failed in branch CH1-content-dev-first-patch.patchdata-combine.bfdata.ps5
[Pipeline] build
[Pipeline] }
12:00:36  Failed in branch CH1-content-dev-first-patch.patchdata-combine.bfdata.xbsx
[Pipeline] // parallel
[Pipeline] }
[Pipeline] // script
[Pipeline] }
[Pipeline] // stage
[Pipeline] }
[Pipeline] // timestamps
[Pipeline] }
[Pipeline] // node
[Pipeline] End of Pipeline
ERROR: No item named CH1-content-dev-first-patch.patchdata-combine.bfdata.win64 found

But with master now we have this error with the job CH1-content-dev-first-patch.combined_bundles.ps5

elipy --location DiceStockholm --use-fbenv-core combined_bundles ps5 --code-branch CH1-content-dev --code-changelist 24522669 --data-branch CH1-content-dev-first-patch --data-changelist 24522669 --combine-code-branch CH1-SP-content-dev --combine-code-changelist  --combine-data-branch CH1-SP-content-dev-first-patch --combine-data-changelist  --data-directory bfdata  
14:36:32 Executing cli.bat
14:36:32 Executing fbenvstartup
14:36:33 
14:36:33 -- Locating TnT folder
14:36:33 Created FbEnvConfigService Session with id: 7ac75756-d9c4-46fc-b702-c53f1c3cd058
14:36:33 Found TNT_ROOT at D:\dev\TnT
14:36:33 
14:36:33 -- Setting environment.
14:36:33 Run: fbenv initialization on D:\dev
14:36:35 No extra environment found in fbcli_init.bat files
14:36:35 Extra environment read from (took 0.2388 s):
14:36:35 	D:\dev\TnT\Code\DICE\BattlefieldGame\fbcli\fbcli_init.bat
14:36:35 	D:\dev\TnT\Bin\fbcli\fbcli_init.bat
14:36:35 
14:36:36 Skipping clink init for automation build.
14:36:39 2025-07-21 13:36:39 elipy2 [WARNING]: Got python\virtual\Lib\site-packages\dice_elipy_scripts\yml\elipy_bct.yml for ELIPY_CONFIG, relative paths must be from %GAME_ROOT%.
14:36:39 2025-07-21 13:36:39 elipy2 [INFO]: Found ELIPY config file at D:\dev\python\virtual\Lib\site-packages\dice_elipy_scripts\yml\elipy_bct.yml 
14:36:39 2025-07-21 13:36:39 elipy2 [INFO]: Initializing ELIPY2 using config file: D:\dev\python\virtual\Lib\site-packages\dice_elipy_scripts\yml\elipy_bct.yml
14:36:39 2025-07-21 13:36:39 elipy2 [INFO]: Elipy was installed from AF2.
14:36:40 2025-07-21 13:36:40 elipy2 [INFO]: elipy2 version: 17.3.9185
14:36:40 2025-07-21 13:36:40 elipy2 [INFO]: dice_elipy_scripts version: 10.2.14232
14:36:40 Usage: elipy combined_bundles [OPTIONS] PLATFORM
14:36:40 Try 'elipy combined_bundles -h' for help.
14:36:40 
14:36:40 Error: Missing option '--data-directory'.


a hint from a colleague:
the patchdata start job triggered the combine_bundles build jobs - instead of the patchdata build jobs. 
The patchdata start job doesn't have the concept of a combined changelist (and shouldn't have), which is why it failed

