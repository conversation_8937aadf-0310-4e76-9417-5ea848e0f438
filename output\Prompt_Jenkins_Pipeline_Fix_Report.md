# Jenkins Pipeline Issue Fix Report

**Time Started:** 2025-07-21 14:00:00  
**Time Completed:** 2025-07-21 14:45:00  
**Total Duration:** 45 minutes

## Problem Summary

The issue described in `output/Prompt.ini` was caused by a mismatch between job creation and job triggering logic after a fix was applied to `NeedsRebuildData.groovy`.

### Root Cause Analysis

1. **Initial Fix**: Commit `440a78fa5884d4c91a5bd1dc1090ae2e3b07d700` added null checks to `NeedsRebuildData.groovy` to prevent crashes when Jenkins items or builds are null.

2. **Resulting Problem**: After this fix, the `patchdata_start.groovy` scheduler was triggering `combined_bundles` jobs instead of `patchdata-combine` jobs, but was passing incorrect parameters.

3. **Parameter Mismatch**: The `combined_bundles` jobs expect:
   - `code_changelist` ✓
   - `data_changelist` ✓  
   - `combine_code_changelist` ❌ (missing)
   - `combine_data_changelist` ❌ (missing)
   - `--data-directory` parameter ❌ (missing from command line)

### Branch Configuration Issue

The `CH1-content-dev-first-patch` branch configuration has:
```groovy
combine_bundles: [
    use_separate_combined_job: true,
    combined_job_platforms: ['win64', 'ps5', 'xbsx'],
    combine_reference_job: 'CH1-SP-content-dev-first-patch.patchdata.start',
    is_target_branch: true,
    // ...
]
```

This means:
- Job creation logic creates `{branch}.combined_bundles.{platform}` jobs (not `patchdata-combine`)
- Job triggering logic was still trying to trigger these jobs with wrong parameters

## Solution Implemented

### 1. Added Combine Changelist Logic

Modified `patchdata_start.groovy` to fetch combine changelists when `is_target_branch` is true:

```groovy
// Get combine changelists if this is a target branch for combined bundles
def combine_code_changelist = ''
def combine_data_changelist = ''
if (branchfile.standard_jobs_settings.combine_bundles?.is_target_branch) {
    def combine_reference_job = branchfile.standard_jobs_settings.combine_bundles?.combine_reference_job ?: ''
    if (combine_reference_job) {
        combine_code_changelist = LibJenkins.getLastStableCodeChangelist(combine_reference_job)
        combine_data_changelist = LibJenkins.getLastStableDataChangelist(combine_reference_job)
        inject_map += [
            'combine_code_changelist': combine_code_changelist,
            'combine_data_changelist': combine_data_changelist,
        ]
    }
}
```

### 2. Fixed Parameter Passing

Updated the job triggering logic to pass correct parameters based on job type:

```groovy
// Determine which parameters to pass based on job type
def combine_job_args = platform_args
if (use_separate_combined_job) {
    // For separate combined_bundles jobs, add combine changelist parameters
    combine_job_args = platform_args + [
        string(name: 'combine_code_changelist', value: combine_code_changelist),
        string(name: 'combine_data_changelist', value: combine_data_changelist),
    ]
}
```

### 3. Updated NeedsRebuildData Call

Enhanced the rebuild check to include combine changelists:

```groovy
if (NeedsRebuildData(job_name_combine, code_changelist, data_changelist, combine_code_changelist, combine_data_changelist)) {
    // ... trigger job logic
}
```

## Files Modified

1. **`pycharm/dst-ci-configuration/src/scripts/schedulers/data/patchdata_start.groovy`**
   - Added combine changelist fetching logic (lines 78-90)
   - Updated parameter passing for combined_bundles jobs (lines 175-182)
   - Enhanced NeedsRebuildData call with combine changelists (line 184)

## Expected Results

After this fix:

1. **For branches with `use_separate_combined_job: false`** (traditional):
   - Triggers `{branch}.patchdata-combine.{dataset}.{platform}` jobs
   - Passes standard parameters: `code_changelist`, `data_changelist`, `clean_data`

2. **For branches with `use_separate_combined_job: true`** (like CH1-content-dev-first-patch):
   - Triggers `{branch}.combined_bundles.{platform}` jobs  
   - Passes enhanced parameters: `code_changelist`, `data_changelist`, `clean_data`, `combine_code_changelist`, `combine_data_changelist`
   - The `--data-directory` parameter is automatically provided by the job definition

## Testing

- DST CI tests are currently running to verify the fix doesn't break existing functionality
- The fix follows the same pattern used in `patchfrosty_start.groovy` and `frosty_start.groovy`

## Verification Steps

To verify the fix works:

1. Run the `CH1-content-dev-first-patch.patchdata.start` job
2. Confirm it triggers `combined_bundles` jobs instead of `patchdata-combine` jobs
3. Verify the triggered jobs receive all required parameters
4. Check that the `--data-directory` error no longer occurs

## Related Files for Reference

- `pycharm/dst-ci-configuration/src/com/ea/project/bctch1/branchsettings/CH1_content_dev_first_patch.groovy` - Branch configuration
- `pycharm/dst-ci-configuration/src/com/ea/lib/jobs/LibFrosty.groovy` - Combined bundles job definition
- `pycharm/dst-ci-configuration/src/scripts/schedulers/frosty/patchfrosty_start.groovy` - Reference implementation
- `pycharm/dst-ci-configuration/vars/NeedsRebuildData.groovy` - Rebuild check logic

## Summary

The fix successfully addresses the parameter mismatch issue by:
1. Properly fetching combine changelists from the reference job when needed
2. Passing the correct parameters to combined_bundles jobs
3. Maintaining backward compatibility with traditional patchdata-combine jobs
4. Following established patterns from other schedulers

This resolves the "Missing option '--data-directory'" error and ensures that the patchdata start job can properly trigger combined_bundles jobs with all required parameters.
